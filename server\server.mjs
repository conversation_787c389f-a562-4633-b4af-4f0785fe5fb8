import express from 'express'
import cors from 'cors'
import 'dotenv/config'
import './db/conn.mjs'
import authRoutes from './routes/auth.mjs'

const app = express()
const PORT = process.env.PORT || 5055

app.use(cors())
app.use(express.json())

app.use('/api/auth', authRoutes)

app.get('/api/ping', (req, res) => {
  res.json({ message: 'pong' })
})

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)
})
